package com.example.ddd.aviation.api.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * ApiConfiguration
 * 
 * Configuration class for API layer concerns.
 * This class handles cross-cutting configuration that affects the entire API layer.
 * 
 * Key configurations:
 * - CORS (Cross-Origin Resource Sharing) settings
 * - Async processing enablement
 * - API versioning strategies
 * - Content negotiation
 * - Security configurations (in a real app)
 */
@Configuration
@EnableAsync // Enables asynchronous event processing
public class ApiConfiguration implements WebMvcConfigurer {
    
    /**
     * Configure CORS settings for the API
     * This allows the API to be called from web browsers with different origins
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
            .allowedOrigins("http://localhost:3000", "http://localhost:8080") // Frontend origins
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
            .allowedHeaders("*")
            .allowCredentials(true)
            .maxAge(3600); // Cache preflight response for 1 hour
    }
}
