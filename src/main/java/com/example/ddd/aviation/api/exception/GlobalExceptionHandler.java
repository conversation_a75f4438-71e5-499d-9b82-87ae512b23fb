package com.example.ddd.aviation.api.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;

/**
 * GlobalExceptionHandler
 * 
 * This is a global exception handler for the API layer.
 * It provides centralized exception handling across all controllers,
 * ensuring consistent error responses and proper HTTP status codes.
 * 
 * Key responsibilities:
 * - Convert domain/application exceptions to appropriate HTTP responses
 * - Provide consistent error response format
 * - Log errors appropriately
 * - Hide internal implementation details from external clients
 * - Map different exception types to appropriate HTTP status codes
 * 
 * This follows the principle of fail-fast and provide meaningful error messages
 * while maintaining security by not exposing internal system details.
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    /**
     * Handles IllegalArgumentException (business validation errors)
     * These typically represent client errors (400 Bad Request)
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        log.warn("Business validation error: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "VALIDATION_ERROR",
            ex.getMessage(),
            LocalDateTime.now(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    /**
     * Handles IllegalStateException (business rule violations)
     * These typically represent client errors (409 Conflict)
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ErrorResponse> handleIllegalStateException(
            IllegalStateException ex, WebRequest request) {
        
        log.warn("Business rule violation: {}", ex.getMessage());
        
        ErrorResponse errorResponse = new ErrorResponse(
            "BUSINESS_RULE_VIOLATION",
            ex.getMessage(),
            LocalDateTime.now(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse);
    }
    
    /**
     * Handles RuntimeException (general application errors)
     * These typically represent server errors (500 Internal Server Error)
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorResponse> handleRuntimeException(
            RuntimeException ex, WebRequest request) {
        
        log.error("Runtime error occurred", ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
            "INTERNAL_ERROR",
            "An internal error occurred. Please try again later.",
            LocalDateTime.now(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * Handles all other exceptions
     * Provides a fallback for unexpected errors
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, WebRequest request) {
        
        log.error("Unexpected error occurred", ex);
        
        ErrorResponse errorResponse = new ErrorResponse(
            "UNEXPECTED_ERROR",
            "An unexpected error occurred. Please contact support if the problem persists.",
            LocalDateTime.now(),
            request.getDescription(false)
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    /**
     * ErrorResponse DTO for consistent error formatting
     */
    public record ErrorResponse(
        String errorCode,
        String message,
        LocalDateTime timestamp,
        String path
    ) {}
}
