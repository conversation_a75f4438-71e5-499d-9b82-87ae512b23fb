package com.example.ddd.aviation.api.controller;

import com.example.ddd.aviation.application.dto.ConfirmTicketCommand;
import com.example.ddd.aviation.application.dto.CreateTicketCommand;
import com.example.ddd.aviation.application.dto.TicketDto;
import com.example.ddd.aviation.application.service.TicketApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * TicketController
 * 
 * This is a REST Controller in the API Layer (also called Presentation Layer or Interface Layer).
 * The API Layer is responsible for:
 * - Exposing application functionality to external clients
 * - Handling HTTP requests and responses
 * - Converting between HTTP data formats and application DTOs
 * - Providing a stable interface for external consumers
 * - Handling API-specific concerns (authentication, rate limiting, etc.)
 * 
 * Key characteristics of API Layer controllers:
 * - Thin layer with minimal logic
 * - Delegate business operations to Application Services
 * - Handle HTTP-specific concerns (status codes, headers, etc.)
 * - Provide clear, RESTful interfaces
 * - Include proper error handling and validation
 * 
 * This controller demonstrates a clean separation of concerns:
 * - Controller handles HTTP concerns
 * - Application Service handles use case orchestration
 * - Domain Layer handles business logic
 * - Infrastructure Layer handles technical concerns
 */
@RestController
@RequestMapping("/api/tickets")
@RequiredArgsConstructor
@Slf4j
public class TicketController {
    
    private final TicketApplicationService ticketApplicationService;
    
    /**
     * Creates a new ticket
     * 
     * POST /api/tickets
     * 
     * @param command the ticket creation request
     * @return created ticket information
     */
    @PostMapping
    public ResponseEntity<TicketDto> createTicket(@RequestBody CreateTicketCommand command) {
        log.info("Creating ticket: {}", command);
        
        try {
            TicketDto createdTicket = ticketApplicationService.createTicket(command);
            log.info("Successfully created ticket: {}", createdTicket.getTicketId());
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdTicket);
            
        } catch (IllegalArgumentException e) {
            log.warn("Invalid ticket creation request: {}", e.getMessage());
            throw e; // Will be handled by global exception handler
        } catch (Exception e) {
            log.error("Error creating ticket: {}", command, e);
            throw e; // Will be handled by global exception handler
        }
    }
    
    /**
     * Confirms a ticket (typically after payment)
     * 
     * PUT /api/tickets/{ticketId}/confirm
     * 
     * @param ticketId the ticket ID to confirm
     * @return confirmed ticket information
     */
    @PutMapping("/{ticketId}/confirm")
    public ResponseEntity<TicketDto> confirmTicket(@PathVariable String ticketId) {
        log.info("Confirming ticket: {}", ticketId);
        
        try {
            ConfirmTicketCommand command = new ConfirmTicketCommand(ticketId);
            TicketDto confirmedTicket = ticketApplicationService.confirmTicket(command);
            log.info("Successfully confirmed ticket: {}", ticketId);
            
            return ResponseEntity.ok(confirmedTicket);
            
        } catch (IllegalArgumentException e) {
            log.warn("Invalid ticket confirmation request for ticket {}: {}", ticketId, e.getMessage());
            throw e; // Will be handled by global exception handler
        } catch (Exception e) {
            log.error("Error confirming ticket: {}", ticketId, e);
            throw e; // Will be handled by global exception handler
        }
    }
    
    /**
     * Retrieves a ticket by ID
     * 
     * GET /api/tickets/{ticketId}
     * 
     * @param ticketId the ticket ID to retrieve
     * @return ticket information if found
     */
    @GetMapping("/{ticketId}")
    public ResponseEntity<TicketDto> getTicket(@PathVariable String ticketId) {
        log.debug("Retrieving ticket: {}", ticketId);
        
        Optional<TicketDto> ticket = ticketApplicationService.getTicket(ticketId);
        
        if (ticket.isPresent()) {
            log.debug("Found ticket: {}", ticketId);
            return ResponseEntity.ok(ticket.get());
        } else {
            log.debug("Ticket not found: {}", ticketId);
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * Retrieves all tickets for a customer
     * 
     * GET /api/tickets?customerId={customerId}
     * 
     * @param customerId the customer ID to search for
     * @return list of tickets for the customer
     */
    @GetMapping
    public ResponseEntity<List<TicketDto>> getTicketsByCustomer(@RequestParam String customerId) {
        log.debug("Retrieving tickets for customer: {}", customerId);
        
        List<TicketDto> tickets = ticketApplicationService.getTicketsByCustomer(customerId);
        log.debug("Found {} tickets for customer: {}", tickets.size(), customerId);
        
        return ResponseEntity.ok(tickets);
    }
    
    /**
     * Health check endpoint
     * 
     * GET /api/tickets/health
     * 
     * @return simple health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Ticket service is healthy");
    }
}
