package com.example.ddd.aviation.domain.service;

import com.example.ddd.aviation.domain.model.Customer;
import com.example.ddd.aviation.domain.model.TicketId;

import java.time.LocalDateTime;

/**
 * TicketValidationService Domain Service
 * 
 * This Domain Service encapsulates validation logic that spans multiple domain concepts
 * and doesn't naturally belong to a single entity or value object.
 * 
 * It contains business rules and validations that are:
 * - Too complex for simple entity validation
 * - Involve multiple domain objects or external factors
 * - Represent business policies that might change
 * - Need to be reused across different use cases
 * 
 * Examples of validation logic that belongs here:
 * - Flight schedule validation
 * - Customer eligibility checks
 * - Business rule compliance
 * - Cross-entity validation
 */
public interface TicketValidationService {
    
    /**
     * Validates if a ticket can be created with the given parameters
     * This method encapsulates complex business rules that determine
     * whether a ticket creation request is valid
     * 
     * @param ticketId the proposed ticket ID
     * @param customer the customer requesting the ticket
     * @param flightNumber the flight number
     * @param origin departure airport
     * @param destination arrival airport
     * @param departureTime when the flight departs
     * @throws IllegalArgumentException if validation fails
     */
    void validateTicketCreation(TicketId ticketId, Customer customer, String flightNumber,
                               String origin, String destination, LocalDateTime departureTime);
    
    /**
     * Validates if the flight schedule is valid for booking
     * Checks business rules around flight timing and availability
     * 
     * @param flightNumber the flight number to validate
     * @param departureTime the proposed departure time
     * @return true if flight schedule is valid for booking
     */
    boolean isFlightScheduleValid(String flightNumber, LocalDateTime departureTime);
    
    /**
     * Validates if the route (origin to destination) is valid
     * Checks if the airline operates on this route and if airports are valid
     * 
     * @param origin departure airport code
     * @param destination arrival airport code
     * @return true if route is valid and operational
     */
    boolean isRouteValid(String origin, String destination);
    
    /**
     * Validates if the customer is eligible to book tickets
     * Checks customer status, restrictions, and business rules
     * 
     * @param customer the customer to validate
     * @return true if customer is eligible to book
     */
    boolean isCustomerEligible(Customer customer);
    
    /**
     * Validates if the departure time is within acceptable booking window
     * Business rules around how far in advance or how close to departure
     * tickets can be booked
     * 
     * @param departureTime the proposed departure time
     * @param bookingTime when the booking is being made
     * @return true if timing is within acceptable window
     */
    boolean isDepartureTimeValid(LocalDateTime departureTime, LocalDateTime bookingTime);
    
    /**
     * Validates airport code format and existence
     * Ensures airport codes follow proper format and represent real airports
     * 
     * @param airportCode the airport code to validate
     * @return true if airport code is valid
     */
    boolean isAirportCodeValid(String airportCode);
    
    /**
     * Validates flight number format and existence
     * Ensures flight numbers follow airline standards and represent real flights
     * 
     * @param flightNumber the flight number to validate
     * @return true if flight number is valid
     */
    boolean isFlightNumberValid(String flightNumber);
    
    /**
     * Checks if there are any booking restrictions for the given route and time
     * Some routes or times might have special restrictions (e.g., seasonal, regulatory)
     * 
     * @param origin departure airport
     * @param destination arrival airport
     * @param departureTime departure time
     * @return true if there are no restrictions preventing booking
     */
    boolean hasNoBookingRestrictions(String origin, String destination, LocalDateTime departureTime);
}
