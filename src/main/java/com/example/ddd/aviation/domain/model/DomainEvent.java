package com.example.ddd.aviation.domain.model;

import java.time.LocalDateTime;

/**
 * DomainEvent Interface
 * 
 * This is a marker interface that represents a domain event in DDD.
 * Domain events are used to capture something important that happened in the domain.
 * They help decouple different parts of the system and enable eventual consistency.
 * 
 * Key characteristics:
 * - Immutable (once created, cannot be changed)
 * - Contains all necessary information about what happened
 * - Named in past tense (e.g., TicketCreated, PaymentProcessed)
 * - Should be published after the aggregate state change is persisted
 */
public interface DomainEvent {
    
    /**
     * Returns when the event occurred
     * @return the timestamp when the event was created
     */
    LocalDateTime getOccurredOn();
    
    /**
     * Returns the version of the event for evolution purposes
     * @return event version number
     */
    default int getEventVersion() {
        return 1;
    }
}
