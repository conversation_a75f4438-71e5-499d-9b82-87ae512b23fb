package com.example.ddd.aviation.domain.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Currency;

/**
 * Money Value Object
 * 
 * This is a Value Object in DDD that represents monetary amounts.
 * Value Objects are immutable objects that are defined by their attributes rather than identity.
 * They have no conceptual identity and are used to describe characteristics of entities.
 * 
 * Key characteristics of Value Objects:
 * - Immutable (cannot be changed after creation)
 * - Equality based on attributes, not identity
 * - No side effects in methods
 * - Can contain business logic related to their concept
 * 
 * This Money value object encapsulates amount and currency, ensuring monetary calculations
 * are always performed with proper precision and currency awareness.
 */
@Getter
@EqualsAndHashCode
public final class Money {
    
    private final BigDecimal amount;
    private final Currency currency;
    
    /**
     * Private constructor to ensure proper validation
     * @param amount the monetary amount
     * @param currency the currency
     */
    private Money(BigDecimal amount, Currency currency) {
        if (amount == null) {
            throw new IllegalArgumentException("Amount cannot be null");
        }
        if (currency == null) {
            throw new IllegalArgumentException("Currency cannot be null");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Amount cannot be negative");
        }
        
        this.amount = amount.setScale(currency.getDefaultFractionDigits(), RoundingMode.HALF_UP);
        this.currency = currency;
    }
    
    /**
     * Factory method to create Money instance
     * @param amount the monetary amount
     * @param currency the currency code (e.g., "USD", "EUR")
     * @return new Money instance
     */
    public static Money of(BigDecimal amount, String currency) {
        return new Money(amount, Currency.getInstance(currency));
    }
    
    /**
     * Factory method to create Money instance with Currency object
     * @param amount the monetary amount
     * @param currency the currency
     * @return new Money instance
     */
    public static Money of(BigDecimal amount, Currency currency) {
        return new Money(amount, currency);
    }
    
    /**
     * Factory method to create Money instance from double
     * @param amount the monetary amount as double
     * @param currency the currency code
     * @return new Money instance
     */
    public static Money of(double amount, String currency) {
        return new Money(BigDecimal.valueOf(amount), Currency.getInstance(currency));
    }
    
    /**
     * Adds another Money amount to this one
     * Both amounts must have the same currency
     * @param other the Money to add
     * @return new Money instance with the sum
     */
    public Money add(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("Cannot add money with different currencies");
        }
        return new Money(this.amount.add(other.amount), this.currency);
    }
    
    /**
     * Subtracts another Money amount from this one
     * Both amounts must have the same currency
     * @param other the Money to subtract
     * @return new Money instance with the difference
     */
    public Money subtract(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("Cannot subtract money with different currencies");
        }
        BigDecimal result = this.amount.subtract(other.amount);
        return new Money(result, this.currency);
    }
    
    /**
     * Multiplies this Money by a factor
     * @param factor the multiplication factor
     * @return new Money instance with the product
     */
    public Money multiply(BigDecimal factor) {
        return new Money(this.amount.multiply(factor), this.currency);
    }
    
    /**
     * Checks if this Money is greater than another
     * @param other the Money to compare with
     * @return true if this Money is greater
     */
    public boolean isGreaterThan(Money other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("Cannot compare money with different currencies");
        }
        return this.amount.compareTo(other.amount) > 0;
    }
    
    /**
     * Checks if this Money is zero
     * @return true if the amount is zero
     */
    public boolean isZero() {
        return this.amount.compareTo(BigDecimal.ZERO) == 0;
    }
    
    @Override
    public String toString() {
        return String.format("%s %s", currency.getCurrencyCode(), amount);
    }
}
