package com.example.ddd.aviation.domain.repository;

import com.example.ddd.aviation.domain.model.CustomerId;
import com.example.ddd.aviation.domain.model.Ticket;
import com.example.ddd.aviation.domain.model.TicketId;
import com.example.ddd.aviation.domain.model.TicketStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * TicketRepository Interface
 * 
 * This is a Repository interface in DDD. Repositories provide an abstraction over data access,
 * allowing the domain layer to work with aggregates without knowing about persistence details.
 * 
 * Key characteristics of Repository interfaces:
 * - Defined in the domain layer (not infrastructure)
 * - Use domain objects and value objects as parameters and return types
 * - Abstract away persistence technology details
 * - Focus on business-meaningful queries
 * - Follow the collection metaphor (add, remove, find)
 * 
 * The Repository pattern provides several benefits:
 * - Decouples domain logic from persistence concerns
 * - Makes testing easier (can be mocked)
 * - Allows changing persistence technology without affecting domain
 * - Provides a clear contract for data access
 * 
 * Note: The implementation of this interface will be in the infrastructure layer
 */
public interface TicketRepository {
    
    /**
     * Saves a ticket (create or update)
     * This method handles both new tickets and updates to existing ones
     * 
     * @param ticket the ticket to save
     * @return the saved ticket
     */
    Ticket save(Ticket ticket);
    
    /**
     * Finds a ticket by its unique identifier
     * 
     * @param ticketId the ticket ID to search for
     * @return Optional containing the ticket if found, empty otherwise
     */
    Optional<Ticket> findById(TicketId ticketId);
    
    /**
     * Finds all tickets for a specific customer
     * This is a business-meaningful query that supports customer service scenarios
     * 
     * @param customerId the customer ID to search for
     * @return list of tickets belonging to the customer
     */
    List<Ticket> findByCustomerId(CustomerId customerId);
    
    /**
     * Finds tickets by flight number
     * Useful for airline operations and customer service
     * 
     * @param flightNumber the flight number to search for
     * @return list of tickets for the specified flight
     */
    List<Ticket> findByFlightNumber(String flightNumber);
    
    /**
     * Finds tickets by status
     * Useful for operational queries and reporting
     * 
     * @param status the ticket status to filter by
     * @return list of tickets with the specified status
     */
    List<Ticket> findByStatus(TicketStatus status);
    
    /**
     * Finds tickets departing within a date range
     * Useful for operational planning and customer notifications
     * 
     * @param startDate start of the date range
     * @param endDate end of the date range
     * @return list of tickets departing within the specified range
     */
    List<Ticket> findByDepartureTimeBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Finds tickets for a specific route (origin and destination)
     * Useful for route analysis and pricing
     * 
     * @param origin departure airport code
     * @param destination arrival airport code
     * @return list of tickets for the specified route
     */
    List<Ticket> findByRoute(String origin, String destination);
    
    /**
     * Checks if a ticket exists with the given ID
     * Useful for validation scenarios
     * 
     * @param ticketId the ticket ID to check
     * @return true if ticket exists, false otherwise
     */
    boolean existsById(TicketId ticketId);
    
    /**
     * Deletes a ticket by its ID
     * Note: In many business scenarios, you might prefer soft deletion
     * or archiving instead of hard deletion
     * 
     * @param ticketId the ID of the ticket to delete
     */
    void deleteById(TicketId ticketId);
    
    /**
     * Counts total number of tickets
     * Useful for reporting and analytics
     * 
     * @return total count of tickets
     */
    long count();
    
    /**
     * Counts tickets by status
     * Useful for operational dashboards
     * 
     * @param status the status to count
     * @return count of tickets with the specified status
     */
    long countByStatus(TicketStatus status);
}
