package com.example.ddd.aviation.domain.service;

import com.example.ddd.aviation.domain.model.Money;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PricingService Domain Service
 * 
 * This is a Domain Service in DDD. Domain Services contain business logic that:
 * - Doesn't naturally belong to any specific entity or value object
 * - Involves multiple domain objects
 * - Represents a business process or calculation
 * - Is stateless and focused on business operations
 * 
 * Domain Services are different from Application Services:
 * - Domain Services contain business logic and rules
 * - Application Services orchestrate use cases and coordinate between layers
 * 
 * The PricingService encapsulates complex pricing logic that involves multiple factors
 * and business rules. In a real aviation system, pricing would be extremely complex,
 * involving factors like:
 * - Base fare by route
 * - Seasonal adjustments
 * - Demand-based pricing
 * - Early bird discounts
 * - Last-minute pricing
 * - Customer loyalty levels
 * - Booking class
 * - Competition analysis
 * 
 * For our learning example, we'll implement simplified pricing rules.
 */
public interface PricingService {
    
    /**
     * Calculates the price for a ticket based on various business factors
     * 
     * This method encapsulates complex pricing logic that doesn't belong
     * to any single entity. It considers multiple factors to determine
     * the final ticket price.
     * 
     * @param origin departure airport code
     * @param destination arrival airport code
     * @param departureTime when the flight departs
     * @param bookingTime when the booking is being made
     * @param isCustomerLoyal whether the customer has loyalty status
     * @return calculated ticket price
     */
    Money calculateTicketPrice(String origin, String destination, 
                              LocalDateTime departureTime, LocalDateTime bookingTime, 
                              boolean isCustomerLoyal);
    
    /**
     * Calculates base fare for a route
     * This represents the fundamental cost of flying between two airports
     * 
     * @param origin departure airport code
     * @param destination arrival airport code
     * @return base fare for the route
     */
    Money calculateBaseFare(String origin, String destination);
    
    /**
     * Applies seasonal pricing adjustments
     * Prices can vary based on the time of year (peak/off-peak seasons)
     * 
     * @param baseFare the base fare to adjust
     * @param departureTime when the flight departs
     * @return adjusted fare with seasonal pricing
     */
    Money applySeasonalPricing(Money baseFare, LocalDateTime departureTime);
    
    /**
     * Applies early bird discount for advance bookings
     * Customers who book early often get discounted prices
     * 
     * @param currentFare the current fare to adjust
     * @param departureTime when the flight departs
     * @param bookingTime when the booking is being made
     * @return fare with early bird discount applied if applicable
     */
    Money applyEarlyBirdDiscount(Money currentFare, LocalDateTime departureTime, LocalDateTime bookingTime);
    
    /**
     * Applies last-minute pricing for bookings close to departure
     * Last-minute bookings often have premium pricing
     * 
     * @param currentFare the current fare to adjust
     * @param departureTime when the flight departs
     * @param bookingTime when the booking is being made
     * @return fare with last-minute pricing applied if applicable
     */
    Money applyLastMinutePricing(Money currentFare, LocalDateTime departureTime, LocalDateTime bookingTime);
    
    /**
     * Applies loyalty discount for loyal customers
     * Loyal customers often receive discounted pricing
     * 
     * @param currentFare the current fare to adjust
     * @param isCustomerLoyal whether the customer has loyalty status
     * @return fare with loyalty discount applied if applicable
     */
    Money applyLoyaltyDiscount(Money currentFare, boolean isCustomerLoyal);
    
    /**
     * Calculates distance-based pricing component
     * Longer flights typically cost more than shorter ones
     * 
     * @param origin departure airport code
     * @param destination arrival airport code
     * @return distance-based pricing component
     */
    BigDecimal calculateDistanceMultiplier(String origin, String destination);
}
