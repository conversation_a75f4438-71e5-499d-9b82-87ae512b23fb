package com.example.ddd.aviation.domain.model;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * AggregateRoot Abstract Class
 * 
 * This is the base class for all aggregate roots in our domain.
 * An Aggregate Root is the only entry point to access and modify an aggregate.
 * It ensures consistency boundaries and manages domain events.
 * 
 * Key responsibilities:
 * - Maintain aggregate consistency
 * - Collect and manage domain events
 * - Provide a single entry point for aggregate operations
 * - Ensure business invariants are maintained
 * 
 * In DDD, an aggregate is a cluster of domain objects that can be treated as a single unit.
 * The aggregate root is the only member of the aggregate that outside objects are allowed to hold references to.
 */
public abstract class AggregateRoot<ID> {
    
    /**
     * List of domain events that occurred during the aggregate's lifecycle
     * These events will be published after the aggregate is successfully persisted
     */
    private final List<DomainEvent> domainEvents = new ArrayList<>();
    
    /**
     * The unique identifier of the aggregate
     */
    protected ID id;
    
    /**
     * Version for optimistic locking and event ordering
     */
    protected Long version;
    
    /**
     * Gets the aggregate's unique identifier
     * @return the aggregate ID
     */
    public ID getId() {
        return id;
    }
    
    /**
     * Gets the aggregate version for optimistic locking
     * @return the version number
     */
    public Long getVersion() {
        return version;
    }
    
    /**
     * Adds a domain event to be published later
     * This method should be called whenever something important happens in the domain
     * 
     * @param event the domain event to add
     */
    protected void addDomainEvent(DomainEvent event) {
        this.domainEvents.add(event);
    }
    
    /**
     * Returns all domain events that have occurred
     * This is typically called by the infrastructure layer to publish events
     * 
     * @return unmodifiable list of domain events
     */
    public List<DomainEvent> getDomainEvents() {
        return Collections.unmodifiableList(domainEvents);
    }
    
    /**
     * Clears all domain events
     * This should be called after events have been successfully published
     */
    public void clearDomainEvents() {
        this.domainEvents.clear();
    }
    
    /**
     * Checks if the aggregate has any unpublished domain events
     * @return true if there are domain events, false otherwise
     */
    public boolean hasDomainEvents() {
        return !domainEvents.isEmpty();
    }
}
