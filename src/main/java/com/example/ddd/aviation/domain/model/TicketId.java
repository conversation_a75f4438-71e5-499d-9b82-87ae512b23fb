package com.example.ddd.aviation.domain.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.UUID;

/**
 * TicketId Value Object
 * 
 * This is a Value Object that represents a ticket identifier.
 * Similar to CustomerId, this provides type safety and prevents mixing up different ID types.
 * 
 * In aviation industry, ticket IDs often follow specific patterns (like PNR - Passenger Name Record),
 * but for simplicity, we're using UUIDs here.
 */
@Getter
@EqualsAndHashCode
public final class TicketId {
    
    private final String value;
    
    /**
     * Private constructor to ensure validation
     * @param value the ticket ID value
     */
    private TicketId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Ticket ID cannot be null or empty");
        }
        this.value = value.trim();
    }
    
    /**
     * Factory method to create TicketId from string
     * @param value the ticket ID value
     * @return new TicketId instance
     */
    public static TicketId of(String value) {
        return new TicketId(value);
    }
    
    /**
     * Factory method to generate a new random TicketId
     * @return new TicketId with random UUID
     */
    public static TicketId generate() {
        return new TicketId(UUID.randomUUID().toString());
    }
    
    @Override
    public String toString() {
        return value;
    }
}
