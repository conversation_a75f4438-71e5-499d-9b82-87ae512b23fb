package com.example.ddd.aviation.domain.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.UUID;

/**
 * CustomerId Value Object
 * 
 * This is a Value Object that represents a customer identifier.
 * Using strongly-typed IDs instead of primitive types (like String or Long) provides several benefits:
 * - Type safety: prevents mixing up different types of IDs
 * - Self-documenting code: makes the intent clear
 * - Encapsulation: can contain validation logic
 * - Refactoring safety: easier to change ID format later
 * 
 * This follows the "Primitive Obsession" code smell avoidance pattern.
 */
@Getter
@EqualsAndHashCode
public final class CustomerId {
    
    private final String value;
    
    /**
     * Private constructor to ensure validation
     * @param value the customer ID value
     */
    private CustomerId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Customer ID cannot be null or empty");
        }
        this.value = value.trim();
    }
    
    /**
     * Factory method to create CustomerId from string
     * @param value the customer ID value
     * @return new CustomerId instance
     */
    public static CustomerId of(String value) {
        return new CustomerId(value);
    }
    
    /**
     * Factory method to generate a new random CustomerId
     * @return new CustomerId with random UUID
     */
    public static CustomerId generate() {
        return new CustomerId(UUID.randomUUID().toString());
    }
    
    @Override
    public String toString() {
        return value;
    }
}
