package com.example.ddd.aviation.domain.model;

/**
 * TicketStatus Enumeration
 * 
 * This enum represents the possible states of a ticket in our domain.
 * Enums are often used in DDD to represent a fixed set of values that have business meaning.
 * 
 * In the aviation industry, tickets go through various states from creation to completion.
 * This enum captures the essential states for our simple ticket creation bounded context.
 */
public enum TicketStatus {
    
    /**
     * Ticket has been created but not yet confirmed or paid
     */
    PENDING("Pending"),
    
    /**
     * Ticket has been confirmed and payment processed
     */
    CONFIRMED("Confirmed"),
    
    /**
     * Ticket has been cancelled by customer or system
     */
    CANCELLED("Cancelled"),
    
    /**
     * Ticket has been used (passenger has traveled)
     */
    USED("Used"),
    
    /**
     * Ticket has expired without being used
     */
    EXPIRED("Expired");
    
    private final String displayName;
    
    TicketStatus(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * Checks if the ticket can be cancelled in its current status
     * @return true if cancellation is allowed
     */
    public boolean canBeCancelled() {
        return this == PENDING || this == CONFIRMED;
    }
    
    /**
     * Checks if the ticket can be confirmed from its current status
     * @return true if confirmation is allowed
     */
    public boolean canBeConfirmed() {
        return this == PENDING;
    }
    
    /**
     * Checks if the ticket can be used from its current status
     * @return true if usage is allowed
     */
    public boolean canBeUsed() {
        return this == CONFIRMED;
    }
}
