package com.example.ddd.aviation.domain.repository;

import com.example.ddd.aviation.domain.model.Customer;
import com.example.ddd.aviation.domain.model.CustomerId;

import java.util.List;
import java.util.Optional;

/**
 * CustomerRepository Interface
 * 
 * Repository interface for Customer entity operations.
 * This interface defines the contract for customer data access without
 * exposing implementation details to the domain layer.
 */
public interface CustomerRepository {
    
    /**
     * Saves a customer (create or update)
     * 
     * @param customer the customer to save
     * @return the saved customer
     */
    Customer save(Customer customer);
    
    /**
     * Finds a customer by their unique identifier
     * 
     * @param customerId the customer ID to search for
     * @return Optional containing the customer if found, empty otherwise
     */
    Optional<Customer> findById(CustomerId customerId);
    
    /**
     * Finds a customer by email address
     * Useful for login scenarios and duplicate prevention
     * 
     * @param email the email address to search for
     * @return Optional containing the customer if found, empty otherwise
     */
    Optional<Customer> findByEmail(String email);
    
    /**
     * Finds customers by name (first or last name contains the search term)
     * Useful for customer service scenarios
     * 
     * @param name the name to search for
     * @return list of customers matching the name criteria
     */
    List<Customer> findByNameContaining(String name);
    
    /**
     * Checks if a customer exists with the given ID
     * 
     * @param customerId the customer ID to check
     * @return true if customer exists, false otherwise
     */
    boolean existsById(CustomerId customerId);
    
    /**
     * Checks if a customer exists with the given email
     * Useful for preventing duplicate registrations
     * 
     * @param email the email to check
     * @return true if customer exists with this email, false otherwise
     */
    boolean existsByEmail(String email);
    
    /**
     * Deletes a customer by their ID
     * 
     * @param customerId the ID of the customer to delete
     */
    void deleteById(CustomerId customerId);
    
    /**
     * Counts total number of customers
     * 
     * @return total count of customers
     */
    long count();
}
