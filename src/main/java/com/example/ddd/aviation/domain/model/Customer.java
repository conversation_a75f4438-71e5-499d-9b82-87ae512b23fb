package com.example.ddd.aviation.domain.model;

import lombok.Getter;

/**
 * Customer Entity
 * 
 * This is an Entity in DDD. Entities are objects that have a distinct identity that runs through time
 * and different representations. They are defined by their identity, not their attributes.
 * 
 * Key characteristics of Entities:
 * - Have a unique identity (ID)
 * - Identity remains constant throughout the entity's lifecycle
 * - Equality is based on identity, not attributes
 * - Can change their attributes over time while maintaining identity
 * - Contain business logic related to their concept
 * 
 * In our ticket creation context, Customer is an entity because:
 * - Each customer has a unique identity
 * - Customer information can change over time (address, phone, etc.)
 * - We need to track the same customer across different tickets
 */
@Getter
public class Customer {
    
    private final CustomerId customerId;
    private String firstName;
    private String lastName;
    private String email;
    private String phoneNumber;
    
    /**
     * Constructor for creating a new customer
     * @param customerId unique customer identifier
     * @param firstName customer's first name
     * @param lastName customer's last name
     * @param email customer's email address
     * @param phoneNumber customer's phone number
     */
    public Customer(CustomerId customerId, String firstName, String lastName, String email, String phoneNumber) {
        if (customerId == null) {
            throw new IllegalArgumentException("Customer ID cannot be null");
        }
        if (firstName == null || firstName.trim().isEmpty()) {
            throw new IllegalArgumentException("First name cannot be null or empty");
        }
        if (lastName == null || lastName.trim().isEmpty()) {
            throw new IllegalArgumentException("Last name cannot be null or empty");
        }
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException("Email cannot be null or empty");
        }
        if (!isValidEmail(email)) {
            throw new IllegalArgumentException("Invalid email format");
        }
        
        this.customerId = customerId;
        this.firstName = firstName.trim();
        this.lastName = lastName.trim();
        this.email = email.trim().toLowerCase();
        this.phoneNumber = phoneNumber != null ? phoneNumber.trim() : null;
    }
    
    /**
     * Updates customer's contact information
     * This method demonstrates how entities can change their attributes while maintaining identity
     * 
     * @param email new email address
     * @param phoneNumber new phone number
     */
    public void updateContactInfo(String email, String phoneNumber) {
        if (email != null && !email.trim().isEmpty()) {
            if (!isValidEmail(email)) {
                throw new IllegalArgumentException("Invalid email format");
            }
            this.email = email.trim().toLowerCase();
        }
        
        if (phoneNumber != null) {
            this.phoneNumber = phoneNumber.trim();
        }
    }
    
    /**
     * Gets the customer's full name
     * @return formatted full name
     */
    public String getFullName() {
        return firstName + " " + lastName;
    }
    
    /**
     * Simple email validation
     * In a real application, you might use a more sophisticated validation library
     * 
     * @param email email to validate
     * @return true if email format is valid
     */
    private boolean isValidEmail(String email) {
        return email != null && email.contains("@") && email.contains(".");
    }
    
    /**
     * Entities are equal if they have the same identity
     * This is different from value objects which are equal based on attributes
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Customer customer = (Customer) obj;
        return customerId.equals(customer.customerId);
    }
    
    @Override
    public int hashCode() {
        return customerId.hashCode();
    }
    
    @Override
    public String toString() {
        return String.format("Customer{id=%s, name=%s, email=%s}", 
                           customerId, getFullName(), email);
    }
}
