package com.example.ddd.aviation.domain.model;

import lombok.Getter;

import java.time.LocalDateTime;

/**
 * TicketCreatedEvent Domain Event
 * 
 * This is a Domain Event that represents the fact that a ticket has been created.
 * Domain Events are a key pattern in DDD for capturing important business occurrences.
 * 
 * Key characteristics of Domain Events:
 * - Immutable (all fields are final)
 * - Named in past tense (something that already happened)
 * - Contains all relevant information about what occurred
 * - Can be used to trigger side effects in other parts of the system
 * - Enable loose coupling between different bounded contexts
 * 
 * This event might trigger various actions such as:
 * - Sending confirmation email to customer
 * - Updating inventory systems
 * - Logging for audit purposes
 * - Triggering pricing calculations
 * - Notifying external systems
 */
@Getter
public final class TicketCreatedEvent implements DomainEvent {
    
    private final TicketId ticketId;
    private final CustomerId customerId;
    private final String flightNumber;
    private final String origin;
    private final String destination;
    private final LocalDateTime departureTime;
    private final Money price;
    private final LocalDateTime occurredOn;
    
    /**
     * Constructor for TicketCreatedEvent
     * 
     * @param ticketId the ID of the created ticket
     * @param customerId the ID of the customer who owns the ticket
     * @param flightNumber the flight number
     * @param origin departure airport
     * @param destination arrival airport
     * @param departureTime when the flight departs
     * @param price ticket price
     * @param occurredOn when the event occurred
     */
    public TicketCreatedEvent(TicketId ticketId, CustomerId customerId, String flightNumber,
                             String origin, String destination, LocalDateTime departureTime,
                             Money price, LocalDateTime occurredOn) {
        this.ticketId = ticketId;
        this.customerId = customerId;
        this.flightNumber = flightNumber;
        this.origin = origin;
        this.destination = destination;
        this.departureTime = departureTime;
        this.price = price;
        this.occurredOn = occurredOn;
    }
    
    @Override
    public String toString() {
        return String.format("TicketCreatedEvent{ticketId=%s, customerId=%s, flight=%s, route=%s→%s, price=%s, occurredOn=%s}",
                           ticketId, customerId, flightNumber, origin, destination, price, occurredOn);
    }
}
