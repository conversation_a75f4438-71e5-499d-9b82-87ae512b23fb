package com.example.ddd.aviation.domain.model;

import lombok.Getter;

import java.time.LocalDateTime;

/**
 * TicketUsedEvent Domain Event
 * 
 * This domain event is published when a ticket is used (passenger has traveled).
 * This represents the completion of the ticket lifecycle.
 */
@Getter
public final class TicketUsedEvent implements DomainEvent {
    
    private final TicketId ticketId;
    private final CustomerId customerId;
    private final LocalDateTime occurredOn;
    
    public TicketUsedEvent(TicketId ticketId, CustomerId customerId, LocalDateTime occurredOn) {
        this.ticketId = ticketId;
        this.customerId = customerId;
        this.occurredOn = occurredOn;
    }
    
    @Override
    public String toString() {
        return String.format("TicketUsedEvent{ticketId=%s, customerId=%s, occurredOn=%s}",
                           ticketId, customerId, occurredOn);
    }
}
