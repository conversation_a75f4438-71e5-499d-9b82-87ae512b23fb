package com.example.ddd.aviation.domain.model;

import lombok.Getter;

import java.time.LocalDateTime;

/**
 * TicketConfirmedEvent Domain Event
 * 
 * This domain event is published when a ticket is confirmed (typically after payment).
 * It represents an important business milestone in the ticket lifecycle.
 * 
 * This event might trigger:
 * - Sending boarding pass to customer
 * - Updating seat inventory
 * - Notifying airline operations
 * - Triggering loyalty point calculations
 */
@Getter
public final class TicketConfirmedEvent implements DomainEvent {
    
    private final TicketId ticketId;
    private final CustomerId customerId;
    private final LocalDateTime occurredOn;
    
    public TicketConfirmedEvent(TicketId ticketId, CustomerId customerId, LocalDateTime occurredOn) {
        this.ticketId = ticketId;
        this.customerId = customerId;
        this.occurredOn = occurredOn;
    }
    
    @Override
    public String toString() {
        return String.format("TicketConfirmedEvent{ticketId=%s, customerId=%s, occurredOn=%s}",
                           ticketId, customerId, occurredOn);
    }
}
