package com.example.ddd.aviation.domain.model;

import lombok.Getter;

import java.time.LocalDateTime;

/**
 * TicketCancelledEvent Domain Event
 * 
 * This domain event is published when a ticket is cancelled.
 * Cancellation is an important business event that often triggers refund processes
 * and inventory updates.
 */
@Getter
public final class TicketCancelledEvent implements DomainEvent {
    
    private final TicketId ticketId;
    private final CustomerId customerId;
    private final LocalDateTime occurredOn;
    
    public TicketCancelledEvent(TicketId ticketId, CustomerId customerId, LocalDateTime occurredOn) {
        this.ticketId = ticketId;
        this.customerId = customerId;
        this.occurredOn = occurredOn;
    }
    
    @Override
    public String toString() {
        return String.format("TicketCancelledEvent{ticketId=%s, customerId=%s, occurredOn=%s}",
                           ticketId, customerId, occurredOn);
    }
}
