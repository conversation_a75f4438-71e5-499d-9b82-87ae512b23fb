package com.example.ddd.aviation.domain.model;

import lombok.Getter;

import java.time.LocalDateTime;

/**
 * Ticket Aggregate Root
 * 
 * This is the main Aggregate Root in our ticket creation bounded context.
 * An Aggregate Root is a special type of Entity that serves as the entry point to an aggregate.
 * 
 * Key responsibilities of an Aggregate Root:
 * - Maintain consistency boundaries within the aggregate
 * - Ensure business invariants are always satisfied
 * - Control access to entities within the aggregate
 * - Generate and manage domain events
 * - Provide a single point of access for aggregate operations
 * 
 * In our case, Ticket is the aggregate root because:
 * - It's the main entity that external systems interact with
 * - It maintains the consistency of all ticket-related data
 * - It controls the ticket lifecycle and business rules
 * - It generates events when important things happen (like ticket creation)
 */
@Getter
public class Ticket extends AggregateRoot<TicketId> {
    
    private final Customer customer;
    private final String flightNumber;
    private final String origin;
    private final String destination;
    private final LocalDateTime departureTime;
    private final Money price;
    private final LocalDateTime createdAt;
    
    private TicketStatus status;
    private LocalDateTime lastModifiedAt;
    
    /**
     * Constructor for creating a new ticket
     * This constructor enforces business rules and generates domain events
     * 
     * @param ticketId unique ticket identifier
     * @param customer the customer who owns the ticket
     * @param flightNumber the flight number
     * @param origin departure airport code
     * @param destination arrival airport code
     * @param departureTime when the flight departs
     * @param price ticket price
     */
    public Ticket(TicketId ticketId, Customer customer, String flightNumber, 
                  String origin, String destination, LocalDateTime departureTime, Money price) {
        
        // Validate business rules
        validateTicketCreation(ticketId, customer, flightNumber, origin, destination, departureTime, price);
        
        // Initialize aggregate root
        this.id = ticketId;
        this.version = 0L;
        
        // Set ticket properties
        this.customer = customer;
        this.flightNumber = flightNumber.trim().toUpperCase();
        this.origin = origin.trim().toUpperCase();
        this.destination = destination.trim().toUpperCase();
        this.departureTime = departureTime;
        this.price = price;
        this.status = TicketStatus.PENDING;
        this.createdAt = LocalDateTime.now();
        this.lastModifiedAt = this.createdAt;
        
        // Generate domain event
        // This is a key DDD pattern: when something important happens in the domain,
        // we generate an event that can be handled by other parts of the system
        addDomainEvent(new TicketCreatedEvent(
            ticketId,
            customer.getCustomerId(),
            flightNumber,
            origin,
            destination,
            departureTime,
            price,
            LocalDateTime.now()
        ));
    }
    
    /**
     * Confirms the ticket (e.g., after payment is processed)
     * This method demonstrates how aggregate roots control state transitions
     */
    public void confirm() {
        if (!status.canBeConfirmed()) {
            throw new IllegalStateException("Ticket cannot be confirmed in status: " + status);
        }
        
        this.status = TicketStatus.CONFIRMED;
        this.lastModifiedAt = LocalDateTime.now();
        
        // Generate domain event for ticket confirmation
        addDomainEvent(new TicketConfirmedEvent(
            this.id,
            this.customer.getCustomerId(),
            LocalDateTime.now()
        ));
    }
    
    /**
     * Cancels the ticket
     * This method shows how business rules are enforced at the aggregate level
     */
    public void cancel() {
        if (!status.canBeCancelled()) {
            throw new IllegalStateException("Ticket cannot be cancelled in status: " + status);
        }
        
        this.status = TicketStatus.CANCELLED;
        this.lastModifiedAt = LocalDateTime.now();
        
        // Generate domain event for ticket cancellation
        addDomainEvent(new TicketCancelledEvent(
            this.id,
            this.customer.getCustomerId(),
            LocalDateTime.now()
        ));
    }
    
    /**
     * Marks the ticket as used (when passenger travels)
     */
    public void markAsUsed() {
        if (!status.canBeUsed()) {
            throw new IllegalStateException("Ticket cannot be used in status: " + status);
        }
        
        this.status = TicketStatus.USED;
        this.lastModifiedAt = LocalDateTime.now();
        
        addDomainEvent(new TicketUsedEvent(
            this.id,
            this.customer.getCustomerId(),
            LocalDateTime.now()
        ));
    }
    
    /**
     * Checks if the ticket is for a domestic flight
     * This is an example of domain logic that belongs in the aggregate
     * 
     * @return true if origin and destination are the same country
     */
    public boolean isDomesticFlight() {
        // Simplified logic - in real world, you'd have a more sophisticated way
        // to determine if airports are in the same country
        return origin.length() == 3 && destination.length() == 3 && 
               origin.substring(0, 2).equals(destination.substring(0, 2));
    }
    
    /**
     * Gets the flight duration in a human-readable format
     * This is domain logic that belongs with the ticket data
     * 
     * @return formatted flight route
     */
    public String getFlightRoute() {
        return String.format("%s → %s", origin, destination);
    }
    
    /**
     * Validates business rules for ticket creation
     * This is a private method that encapsulates business validation logic
     */
    private void validateTicketCreation(TicketId ticketId, Customer customer, String flightNumber,
                                      String origin, String destination, LocalDateTime departureTime, Money price) {
        if (ticketId == null) {
            throw new IllegalArgumentException("Ticket ID cannot be null");
        }
        if (customer == null) {
            throw new IllegalArgumentException("Customer cannot be null");
        }
        if (flightNumber == null || flightNumber.trim().isEmpty()) {
            throw new IllegalArgumentException("Flight number cannot be null or empty");
        }
        if (origin == null || origin.trim().isEmpty()) {
            throw new IllegalArgumentException("Origin cannot be null or empty");
        }
        if (destination == null || destination.trim().isEmpty()) {
            throw new IllegalArgumentException("Destination cannot be null or empty");
        }
        if (origin.trim().equalsIgnoreCase(destination.trim())) {
            throw new IllegalArgumentException("Origin and destination cannot be the same");
        }
        if (departureTime == null) {
            throw new IllegalArgumentException("Departure time cannot be null");
        }
        if (departureTime.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("Departure time cannot be in the past");
        }
        if (price == null) {
            throw new IllegalArgumentException("Price cannot be null");
        }
        if (price.isZero()) {
            throw new IllegalArgumentException("Price must be greater than zero");
        }
    }
    
    @Override
    public String toString() {
        return String.format("Ticket{id=%s, flight=%s, route=%s, status=%s, customer=%s}", 
                           id, flightNumber, getFlightRoute(), status, customer.getFullName());
    }
}
