package com.example.ddd.aviation.infrastructure.persistence;

import com.example.ddd.aviation.domain.model.TicketStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * TicketJpaRepository
 * 
 * Spring Data JPA Repository interface for TicketJpaEntity.
 * Provides database access methods for ticket persistence.
 */
@Repository
public interface TicketJpaRepository extends JpaRepository<TicketJpaEntity, String> {
    
    /**
     * Finds tickets by customer ID
     */
    List<TicketJpaEntity> findByCustomerId(String customerId);
    
    /**
     * Finds tickets by flight number
     */
    List<TicketJpaEntity> findByFlightNumber(String flightNumber);
    
    /**
     * Finds tickets by status
     */
    List<TicketJpaEntity> findByStatus(TicketStatus status);
    
    /**
     * Finds tickets departing within a date range
     */
    List<TicketJpaEntity> findByDepartureTimeBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Finds tickets by route (origin and destination)
     */
    List<TicketJpaEntity> findByOriginAndDestination(String origin, String destination);
    
    /**
     * Counts tickets by status
     */
    long countByStatus(TicketStatus status);
    
    /**
     * Custom query to find tickets departing today
     */
    @Query("SELECT t FROM TicketJpaEntity t WHERE DATE(t.departureTime) = CURRENT_DATE")
    List<TicketJpaEntity> findTicketsDepartingToday();
    
    /**
     * Custom query to find tickets for a customer with specific status
     */
    @Query("SELECT t FROM TicketJpaEntity t WHERE t.customerId = :customerId AND t.status = :status")
    List<TicketJpaEntity> findByCustomerIdAndStatus(@Param("customerId") String customerId, 
                                                    @Param("status") TicketStatus status);
}
