package com.example.ddd.aviation.infrastructure.persistence;

import com.example.ddd.aviation.domain.model.Customer;
import com.example.ddd.aviation.domain.model.CustomerId;
import com.example.ddd.aviation.domain.repository.CustomerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * CustomerRepositoryImpl
 * 
 * This is the Infrastructure implementation of the CustomerRepository domain interface.
 * It acts as an adapter between the domain layer and the persistence layer.
 * 
 * Key responsibilities:
 * - Implement the domain repository interface
 * - Convert between domain objects and JPA entities
 * - Delegate actual persistence operations to Spring Data JPA repository
 * - Provide an anti-corruption layer protecting domain from infrastructure concerns
 * 
 * This follows the Repository pattern and Adapter pattern:
 * - Repository: provides collection-like interface for aggregates
 * - Adapter: adapts JPA persistence to domain repository interface
 * 
 * The implementation ensures that:
 * - Domain layer remains independent of persistence technology
 * - JPA concerns don't leak into the domain
 * - Domain objects are properly converted to/from persistence objects
 */
@Repository
@RequiredArgsConstructor
public class CustomerRepositoryImpl implements CustomerRepository {
    
    private final CustomerJpaRepository jpaRepository;
    
    @Override
    public Customer save(Customer customer) {
        // Convert domain object to JPA entity
        CustomerJpaEntity jpaEntity = new CustomerJpaEntity(customer);
        
        // Save using Spring Data JPA
        CustomerJpaEntity savedEntity = jpaRepository.save(jpaEntity);
        
        // Convert back to domain object
        return savedEntity.toDomainObject();
    }
    
    @Override
    public Optional<Customer> findById(CustomerId customerId) {
        return jpaRepository.findById(customerId.getValue())
            .map(CustomerJpaEntity::toDomainObject);
    }
    
    @Override
    public Optional<Customer> findByEmail(String email) {
        return jpaRepository.findByEmail(email)
            .map(CustomerJpaEntity::toDomainObject);
    }
    
    @Override
    public List<Customer> findByNameContaining(String name) {
        return jpaRepository.findByNameContaining(name)
            .stream()
            .map(CustomerJpaEntity::toDomainObject)
            .toList();
    }
    
    @Override
    public boolean existsById(CustomerId customerId) {
        return jpaRepository.existsById(customerId.getValue());
    }
    
    @Override
    public boolean existsByEmail(String email) {
        return jpaRepository.existsByEmail(email);
    }
    
    @Override
    public void deleteById(CustomerId customerId) {
        jpaRepository.deleteById(customerId.getValue());
    }
    
    @Override
    public long count() {
        return jpaRepository.count();
    }
}
