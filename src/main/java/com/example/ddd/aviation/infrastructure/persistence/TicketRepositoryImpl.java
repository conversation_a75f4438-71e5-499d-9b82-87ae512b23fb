package com.example.ddd.aviation.infrastructure.persistence;

import com.example.ddd.aviation.domain.model.*;
import com.example.ddd.aviation.domain.repository.TicketRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * TicketRepositoryImpl
 * 
 * Infrastructure implementation of the TicketRepository domain interface.
 * This class bridges the domain and persistence layers for Ticket aggregate operations.
 */
@Repository
@RequiredArgsConstructor
public class TicketRepositoryImpl implements TicketRepository {
    
    private final TicketJpaRepository jpaRepository;
    
    @Override
    public Ticket save(Ticket ticket) {
        // Check if this is an update to existing ticket
        Optional<TicketJpaEntity> existingEntity = jpaRepository.findById(ticket.getId().getValue());
        
        TicketJpaEntity entityToSave;
        if (existingEntity.isPresent()) {
            // Update existing entity
            entityToSave = existingEntity.get();
            entityToSave.updateFromDomainObject(ticket);
        } else {
            // Create new entity
            entityToSave = new TicketJpaEntity(ticket);
        }
        
        // Save using Spring Data JPA
        TicketJpaEntity savedEntity = jpaRepository.save(entityToSave);
        
        // Convert back to domain object
        return savedEntity.toDomainObject();
    }
    
    @Override
    public Optional<Ticket> findById(TicketId ticketId) {
        return jpaRepository.findById(ticketId.getValue())
            .map(TicketJpaEntity::toDomainObject);
    }
    
    @Override
    public List<Ticket> findByCustomerId(CustomerId customerId) {
        return jpaRepository.findByCustomerId(customerId.getValue())
            .stream()
            .map(TicketJpaEntity::toDomainObject)
            .toList();
    }
    
    @Override
    public List<Ticket> findByFlightNumber(String flightNumber) {
        return jpaRepository.findByFlightNumber(flightNumber)
            .stream()
            .map(TicketJpaEntity::toDomainObject)
            .toList();
    }
    
    @Override
    public List<Ticket> findByStatus(TicketStatus status) {
        return jpaRepository.findByStatus(status)
            .stream()
            .map(TicketJpaEntity::toDomainObject)
            .toList();
    }
    
    @Override
    public List<Ticket> findByDepartureTimeBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return jpaRepository.findByDepartureTimeBetween(startDate, endDate)
            .stream()
            .map(TicketJpaEntity::toDomainObject)
            .toList();
    }
    
    @Override
    public List<Ticket> findByRoute(String origin, String destination) {
        return jpaRepository.findByOriginAndDestination(origin, destination)
            .stream()
            .map(TicketJpaEntity::toDomainObject)
            .toList();
    }
    
    @Override
    public boolean existsById(TicketId ticketId) {
        return jpaRepository.existsById(ticketId.getValue());
    }
    
    @Override
    public void deleteById(TicketId ticketId) {
        jpaRepository.deleteById(ticketId.getValue());
    }
    
    @Override
    public long count() {
        return jpaRepository.count();
    }
    
    @Override
    public long countByStatus(TicketStatus status) {
        return jpaRepository.countByStatus(status);
    }
}
