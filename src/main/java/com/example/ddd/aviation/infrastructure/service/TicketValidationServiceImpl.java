package com.example.ddd.aviation.infrastructure.service;

import com.example.ddd.aviation.domain.model.Customer;
import com.example.ddd.aviation.domain.model.TicketId;
import com.example.ddd.aviation.domain.repository.TicketRepository;
import com.example.ddd.aviation.domain.service.TicketValidationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * TicketValidationServiceImpl
 * 
 * Infrastructure implementation of the TicketValidationService domain service.
 * Contains business validation logic that spans multiple domain concepts.
 */
@Service
@RequiredArgsConstructor
public class TicketValidationServiceImpl implements TicketValidationService {
    
    private final TicketRepository ticketRepository;
    
    // Valid airport codes (simplified set for demo)
    private static final Set<String> VALID_AIRPORT_CODES = Set.of(
        "NYC", "LAX", "CHI", "MIA", "DEN", "SEA", "BOS", "ATL", "DFW", "LAS"
    );
    
    // Flight number pattern (simplified)
    private static final Pattern FLIGHT_NUMBER_PATTERN = Pattern.compile("^[A-Z]{2}\\d{3,4}$");
    
    @Override
    public void validateTicketCreation(TicketId ticketId, Customer customer, String flightNumber,
                                      String origin, String destination, LocalDateTime departureTime) {
        
        // Check if ticket ID already exists
        if (ticketRepository.existsById(ticketId)) {
            throw new IllegalArgumentException("Ticket with ID " + ticketId + " already exists");
        }
        
        // Validate customer eligibility
        if (!isCustomerEligible(customer)) {
            throw new IllegalArgumentException("Customer is not eligible to book tickets");
        }
        
        // Validate flight number
        if (!isFlightNumberValid(flightNumber)) {
            throw new IllegalArgumentException("Invalid flight number format: " + flightNumber);
        }
        
        // Validate route
        if (!isRouteValid(origin, destination)) {
            throw new IllegalArgumentException("Invalid route: " + origin + " to " + destination);
        }
        
        // Validate departure time
        if (!isDepartureTimeValid(departureTime, LocalDateTime.now())) {
            throw new IllegalArgumentException("Invalid departure time: " + departureTime);
        }
        
        // Validate flight schedule
        if (!isFlightScheduleValid(flightNumber, departureTime)) {
            throw new IllegalArgumentException("Flight schedule is not valid for booking");
        }
        
        // Check booking restrictions
        if (!hasNoBookingRestrictions(origin, destination, departureTime)) {
            throw new IllegalArgumentException("Booking restrictions apply for this route and time");
        }
    }
    
    @Override
    public boolean isFlightScheduleValid(String flightNumber, LocalDateTime departureTime) {
        // Simplified validation - in reality would check against flight schedules
        
        // Flights don't operate between 2 AM and 5 AM
        int hour = departureTime.getHour();
        if (hour >= 2 && hour < 5) {
            return false;
        }
        
        // No flights on Christmas Day (simplified)
        if (departureTime.getMonthValue() == 12 && departureTime.getDayOfMonth() == 25) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean isRouteValid(String origin, String destination) {
        // Both airports must be valid
        if (!isAirportCodeValid(origin) || !isAirportCodeValid(destination)) {
            return false;
        }
        
        // Origin and destination must be different
        if (origin.equals(destination)) {
            return false;
        }
        
        // All routes are valid for this simplified example
        return true;
    }
    
    @Override
    public boolean isCustomerEligible(Customer customer) {
        // Simplified eligibility check
        
        // Customer must have valid email
        if (customer.getEmail() == null || customer.getEmail().trim().isEmpty()) {
            return false;
        }
        
        // Customer must have valid name
        if (customer.getFirstName() == null || customer.getFirstName().trim().isEmpty() ||
            customer.getLastName() == null || customer.getLastName().trim().isEmpty()) {
            return false;
        }
        
        // In a real system, you might check:
        // - Customer is not on a no-fly list
        // - Customer has valid identification
        // - Customer account is in good standing
        // - Customer meets age requirements for unaccompanied travel
        
        return true;
    }
    
    @Override
    public boolean isDepartureTimeValid(LocalDateTime departureTime, LocalDateTime bookingTime) {
        // Departure must be in the future
        if (departureTime.isBefore(bookingTime)) {
            return false;
        }
        
        // Must be at least 1 hour in advance for domestic flights
        long hoursUntilDeparture = ChronoUnit.HOURS.between(bookingTime, departureTime);
        if (hoursUntilDeparture < 1) {
            return false;
        }
        
        // Cannot book more than 1 year in advance
        long daysUntilDeparture = ChronoUnit.DAYS.between(bookingTime, departureTime);
        if (daysUntilDeparture > 365) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean isAirportCodeValid(String airportCode) {
        if (airportCode == null || airportCode.trim().isEmpty()) {
            return false;
        }
        
        String code = airportCode.trim().toUpperCase();
        
        // Must be 3 characters
        if (code.length() != 3) {
            return false;
        }
        
        // Must be in our valid set (simplified)
        return VALID_AIRPORT_CODES.contains(code);
    }
    
    @Override
    public boolean isFlightNumberValid(String flightNumber) {
        if (flightNumber == null || flightNumber.trim().isEmpty()) {
            return false;
        }
        
        String flight = flightNumber.trim().toUpperCase();
        
        // Must match pattern: 2 letters followed by 3-4 digits
        return FLIGHT_NUMBER_PATTERN.matcher(flight).matches();
    }
    
    @Override
    public boolean hasNoBookingRestrictions(String origin, String destination, LocalDateTime departureTime) {
        // Simplified restriction checks
        
        // No restrictions during normal business hours
        int hour = departureTime.getHour();
        if (hour >= 6 && hour <= 22) {
            return true;
        }
        
        // Red-eye flights (late night/early morning) might have restrictions
        // For simplicity, we'll allow them but in reality there might be
        // crew scheduling, airport operation, or regulatory restrictions
        
        return true;
    }
}
