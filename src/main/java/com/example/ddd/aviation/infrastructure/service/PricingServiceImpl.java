package com.example.ddd.aviation.infrastructure.service;

import com.example.ddd.aviation.domain.model.Money;
import com.example.ddd.aviation.domain.service.PricingService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

/**
 * PricingServiceImpl
 * 
 * Infrastructure implementation of the PricingService domain service.
 * This implementation contains the actual business logic for calculating ticket prices.
 * 
 * In a real aviation system, this would likely:
 * - Connect to external pricing systems
 * - Use complex algorithms and machine learning
 * - Consider real-time market conditions
 * - Access historical pricing data
 * - Apply dynamic pricing strategies
 * 
 * For our learning example, we implement simplified but realistic pricing rules.
 */
@Service
public class PricingServiceImpl implements PricingService {
    
    // Base fares by route (simplified - in reality this would be in a database)
    private static final Map<String, BigDecimal> BASE_FARES = Map.of(
        "NYC-LAX", BigDecimal.valueOf(299.99),
        "LAX-NYC", BigDecimal.valueOf(299.99),
        "NYC-CHI", BigDecimal.valueOf(199.99),
        "CHI-NYC", BigDecimal.valueOf(199.99),
        "LAX-CHI", BigDecimal.valueOf(249.99),
        "CHI-LAX", BigDecimal.valueOf(249.99)
    );
    
    private static final BigDecimal DEFAULT_BASE_FARE = BigDecimal.valueOf(399.99);
    private static final String DEFAULT_CURRENCY = "USD";
    
    @Override
    public Money calculateTicketPrice(String origin, String destination, 
                                     LocalDateTime departureTime, LocalDateTime bookingTime, 
                                     boolean isCustomerLoyal) {
        
        // 1. Start with base fare
        Money baseFare = calculateBaseFare(origin, destination);
        
        // 2. Apply seasonal pricing
        Money seasonalPrice = applySeasonalPricing(baseFare, departureTime);
        
        // 3. Apply early bird or last-minute pricing
        Money timeBasedPrice = applyEarlyBirdDiscount(seasonalPrice, departureTime, bookingTime);
        timeBasedPrice = applyLastMinutePricing(timeBasedPrice, departureTime, bookingTime);
        
        // 4. Apply loyalty discount
        Money finalPrice = applyLoyaltyDiscount(timeBasedPrice, isCustomerLoyal);
        
        return finalPrice;
    }
    
    @Override
    public Money calculateBaseFare(String origin, String destination) {
        String route = origin + "-" + destination;
        BigDecimal fare = BASE_FARES.getOrDefault(route, DEFAULT_BASE_FARE);
        
        // Apply distance multiplier
        BigDecimal distanceMultiplier = calculateDistanceMultiplier(origin, destination);
        BigDecimal adjustedFare = fare.multiply(distanceMultiplier);
        
        return Money.of(adjustedFare, DEFAULT_CURRENCY);
    }
    
    @Override
    public Money applySeasonalPricing(Money baseFare, LocalDateTime departureTime) {
        // Peak season: June-August and December
        int month = departureTime.getMonthValue();
        BigDecimal seasonalMultiplier;
        
        if ((month >= 6 && month <= 8) || month == 12) {
            // Peak season - 20% increase
            seasonalMultiplier = BigDecimal.valueOf(1.20);
        } else if (month >= 1 && month <= 3) {
            // Off-peak season - 10% discount
            seasonalMultiplier = BigDecimal.valueOf(0.90);
        } else {
            // Regular season - no change
            seasonalMultiplier = BigDecimal.ONE;
        }
        
        return baseFare.multiply(seasonalMultiplier);
    }
    
    @Override
    public Money applyEarlyBirdDiscount(Money currentFare, LocalDateTime departureTime, LocalDateTime bookingTime) {
        long daysInAdvance = ChronoUnit.DAYS.between(bookingTime, departureTime);
        
        if (daysInAdvance >= 30) {
            // 15% discount for bookings 30+ days in advance
            return currentFare.multiply(BigDecimal.valueOf(0.85));
        } else if (daysInAdvance >= 14) {
            // 10% discount for bookings 14+ days in advance
            return currentFare.multiply(BigDecimal.valueOf(0.90));
        } else if (daysInAdvance >= 7) {
            // 5% discount for bookings 7+ days in advance
            return currentFare.multiply(BigDecimal.valueOf(0.95));
        }
        
        return currentFare; // No early bird discount
    }
    
    @Override
    public Money applyLastMinutePricing(Money currentFare, LocalDateTime departureTime, LocalDateTime bookingTime) {
        long hoursUntilDeparture = ChronoUnit.HOURS.between(bookingTime, departureTime);
        
        if (hoursUntilDeparture <= 24) {
            // 50% premium for bookings within 24 hours
            return currentFare.multiply(BigDecimal.valueOf(1.50));
        } else if (hoursUntilDeparture <= 72) {
            // 25% premium for bookings within 72 hours
            return currentFare.multiply(BigDecimal.valueOf(1.25));
        }
        
        return currentFare; // No last-minute premium
    }
    
    @Override
    public Money applyLoyaltyDiscount(Money currentFare, boolean isCustomerLoyal) {
        if (isCustomerLoyal) {
            // 10% discount for loyal customers
            return currentFare.multiply(BigDecimal.valueOf(0.90));
        }
        
        return currentFare;
    }
    
    @Override
    public BigDecimal calculateDistanceMultiplier(String origin, String destination) {
        // Simplified distance calculation based on airport codes
        // In reality, this would use actual geographic distances
        
        if (origin.equals(destination)) {
            return BigDecimal.ZERO; // Same airport
        }
        
        // Simulate different distance categories
        String route = origin + "-" + destination;
        
        // Short haul (under 500 miles)
        if (isShortHaul(route)) {
            return BigDecimal.valueOf(0.8);
        }
        
        // Medium haul (500-1500 miles)
        if (isMediumHaul(route)) {
            return BigDecimal.ONE;
        }
        
        // Long haul (over 1500 miles)
        return BigDecimal.valueOf(1.3);
    }
    
    private boolean isShortHaul(String route) {
        // Simplified logic - in reality would use actual distances
        return route.contains("NYC-CHI") || route.contains("CHI-NYC");
    }
    
    private boolean isMediumHaul(String route) {
        // Simplified logic
        return route.contains("LAX-CHI") || route.contains("CHI-LAX");
    }
}
