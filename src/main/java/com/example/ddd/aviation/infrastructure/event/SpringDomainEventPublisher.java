package com.example.ddd.aviation.infrastructure.event;

import com.example.ddd.aviation.application.service.DomainEventPublisher;
import com.example.ddd.aviation.domain.model.DomainEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * SpringDomainEventPublisher
 * 
 * Infrastructure implementation of DomainEventPublisher using Spring's ApplicationEventPublisher.
 * This class bridges domain events with Spring's event publishing mechanism.
 * 
 * Key characteristics:
 * - Uses Spring's built-in event publishing infrastructure
 * - Provides reliable event delivery within the same JVM
 * - Enables loose coupling between event producers and consumers
 * - Supports both synchronous and asynchronous event handling
 * 
 * In a distributed system, you might replace this with:
 * - Message queues (RabbitMQ, Apache Kafka)
 * - Event streaming platforms
 * - Cloud-based event services (AWS EventBridge, Azure Event Grid)
 * 
 * The beauty of the DomainEventPublisher interface is that the domain layer
 * doesn't need to know about these infrastructure details.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SpringDomainEventPublisher implements DomainEventPublisher {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    
    @Override
    public void publish(DomainEvent event) {
        log.info("Publishing domain event: {}", event);
        
        try {
            // Publish the event using Spring's event mechanism
            // This will trigger all registered @EventListener methods
            applicationEventPublisher.publishEvent(event);
            
            log.debug("Successfully published domain event: {}", event.getClass().getSimpleName());
            
        } catch (Exception e) {
            log.error("Failed to publish domain event: {}", event, e);
            // In a production system, you might want to:
            // - Store failed events for retry
            // - Send to a dead letter queue
            // - Trigger alerts for monitoring
            throw new RuntimeException("Failed to publish domain event", e);
        }
    }
}
