package com.example.ddd.aviation.infrastructure.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * InfrastructureConfiguration
 * 
 * Configuration class for infrastructure layer concerns.
 * This class handles technical configuration that affects data persistence,
 * transaction management, and other infrastructure concerns.
 * 
 * Key configurations:
 * - JPA repository scanning
 * - Transaction management
 * - Database connection settings
 * - Event publishing configuration
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.example.ddd.aviation.infrastructure.persistence")
@EnableTransactionManagement
public class InfrastructureConfiguration {
    
    // Additional infrastructure beans can be configured here
    // For example:
    // - Custom transaction managers
    // - Database connection pools
    // - Message queue configurations
    // - External service clients
}
