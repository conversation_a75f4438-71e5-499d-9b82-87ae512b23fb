package com.example.ddd.aviation.infrastructure.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * CustomerJpaRepository
 * 
 * This is a Spring Data JPA Repository interface that provides database access
 * for CustomerJpaEntity. Spring Data JPA automatically implements this interface
 * at runtime, providing standard CRUD operations and custom query methods.
 * 
 * This is part of the Infrastructure Layer and handles the technical concerns
 * of data persistence using JPA/Hibernate.
 */
@Repository
public interface CustomerJpaRepository extends JpaRepository<CustomerJpaEntity, String> {
    
    /**
     * Finds a customer by email address
     * Spring Data JPA automatically implements this based on the method name
     */
    Optional<CustomerJpaEntity> findByEmail(String email);
    
    /**
     * Finds customers by name containing the search term
     * This searches both first name and last name
     */
    @Query("SELECT c FROM CustomerJpaEntity c WHERE " +
           "LOWER(c.firstName) LIKE LOWER(CONCAT('%', :name, '%')) OR " +
           "LOWER(c.lastName) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<CustomerJpaEntity> findByNameContaining(@Param("name") String name);
    
    /**
     * Checks if a customer exists with the given email
     */
    boolean existsByEmail(String email);
}
