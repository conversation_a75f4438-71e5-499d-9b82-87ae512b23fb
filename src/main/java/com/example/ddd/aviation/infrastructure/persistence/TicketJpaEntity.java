package com.example.ddd.aviation.infrastructure.persistence;

import com.example.ddd.aviation.domain.model.*;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;

/**
 * TicketJpaEntity
 * 
 * JPA Entity for persisting Ticket aggregate root.
 * This entity handles the persistence concerns for the Ticket domain object.
 * 
 * Note: In this simple example, we're storing the Customer data denormalized
 * within the Ticket entity. In a more complex system, you might have separate
 * tables and foreign key relationships.
 */
@Entity
@Table(name = "tickets")
@Getter
@Setter
@NoArgsConstructor
public class TicketJpaEntity {
    
    @Id
    @Column(name = "ticket_id", nullable = false)
    private String ticketId;
    
    @Version
    @Column(name = "version")
    private Long version;
    
    // Customer information (denormalized for simplicity)
    @Column(name = "customer_id", nullable = false)
    private String customerId;
    
    @Column(name = "customer_first_name", nullable = false, length = 100)
    private String customerFirstName;
    
    @Column(name = "customer_last_name", nullable = false, length = 100)
    private String customerLastName;
    
    @Column(name = "customer_email", nullable = false, length = 255)
    private String customerEmail;
    
    @Column(name = "customer_phone_number", length = 20)
    private String customerPhoneNumber;
    
    // Flight information
    @Column(name = "flight_number", nullable = false, length = 10)
    private String flightNumber;
    
    @Column(name = "origin", nullable = false, length = 3)
    private String origin;
    
    @Column(name = "destination", nullable = false, length = 3)
    private String destination;
    
    @Column(name = "departure_time", nullable = false)
    private LocalDateTime departureTime;
    
    // Price information
    @Column(name = "price_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal priceAmount;
    
    @Column(name = "price_currency", nullable = false, length = 3)
    private String priceCurrency;
    
    // Status and metadata
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TicketStatus status;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "last_modified_at", nullable = false)
    private LocalDateTime lastModifiedAt;
    
    /**
     * Constructor for creating JPA entity from domain object
     */
    public TicketJpaEntity(Ticket ticket) {
        this.ticketId = ticket.getId().getValue();
        this.version = ticket.getVersion();
        
        // Customer data
        this.customerId = ticket.getCustomer().getCustomerId().getValue();
        this.customerFirstName = ticket.getCustomer().getFirstName();
        this.customerLastName = ticket.getCustomer().getLastName();
        this.customerEmail = ticket.getCustomer().getEmail();
        this.customerPhoneNumber = ticket.getCustomer().getPhoneNumber();
        
        // Flight data
        this.flightNumber = ticket.getFlightNumber();
        this.origin = ticket.getOrigin();
        this.destination = ticket.getDestination();
        this.departureTime = ticket.getDepartureTime();
        
        // Price data
        this.priceAmount = ticket.getPrice().getAmount();
        this.priceCurrency = ticket.getPrice().getCurrency().getCurrencyCode();
        
        // Status and metadata
        this.status = ticket.getStatus();
        this.createdAt = ticket.getCreatedAt();
        this.lastModifiedAt = ticket.getLastModifiedAt();
    }
    
    /**
     * Converts this JPA entity to domain object
     * This reconstruction creates the full aggregate with all its business logic
     * 
     * @return Ticket domain object
     */
    public Ticket toDomainObject() {
        // Reconstruct Customer entity
        Customer customer = new Customer(
            CustomerId.of(this.customerId),
            this.customerFirstName,
            this.customerLastName,
            this.customerEmail,
            this.customerPhoneNumber
        );
        
        // Reconstruct Money value object
        Money price = Money.of(this.priceAmount, Currency.getInstance(this.priceCurrency));
        
        // Create Ticket using reflection or a special constructor
        // Note: In a real implementation, you might need a special reconstruction method
        // or use reflection to set private fields, since the domain constructor
        // generates events and we don't want that during reconstruction
        return reconstructTicket(customer, price);
    }
    
    /**
     * Private method to reconstruct ticket without triggering business logic
     * In a real implementation, this might use reflection or a special factory method
     */
    private Ticket reconstructTicket(Customer customer, Money price) {
        // For simplicity, we'll use the regular constructor
        // In production, you'd want to avoid triggering domain events during reconstruction
        Ticket ticket = new Ticket(
            TicketId.of(this.ticketId),
            customer,
            this.flightNumber,
            this.origin,
            this.destination,
            this.departureTime,
            price
        );
        
        // Clear any events that were generated during reconstruction
        ticket.clearDomainEvents();
        
        return ticket;
    }
    
    /**
     * Updates this JPA entity from domain object
     * Used when updating existing entities
     */
    public void updateFromDomainObject(Ticket ticket) {
        this.version = ticket.getVersion();
        
        // Update customer data
        this.customerFirstName = ticket.getCustomer().getFirstName();
        this.customerLastName = ticket.getCustomer().getLastName();
        this.customerEmail = ticket.getCustomer().getEmail();
        this.customerPhoneNumber = ticket.getCustomer().getPhoneNumber();
        
        // Update status and metadata
        this.status = ticket.getStatus();
        this.lastModifiedAt = ticket.getLastModifiedAt();
        
        // Note: Flight and price data typically don't change after creation
    }
}
