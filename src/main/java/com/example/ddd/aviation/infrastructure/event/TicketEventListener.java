package com.example.ddd.aviation.infrastructure.event;

import com.example.ddd.aviation.domain.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * TicketEventListener
 * 
 * This is an Event Listener that handles domain events related to tickets.
 * Event Listeners are part of the Infrastructure layer and handle side effects
 * that occur when domain events are published.
 * 
 * Key characteristics of Event Listeners:
 * - React to domain events without the domain knowing about them
 * - Handle cross-cutting concerns (logging, notifications, integrations)
 * - Can be synchronous or asynchronous
 * - Enable loose coupling between different parts of the system
 * - Can trigger additional business processes
 * 
 * Common use cases for event listeners:
 * - Sending notifications (email, SMS, push notifications)
 * - Updating read models or projections
 * - Integrating with external systems
 * - Triggering workflows in other bounded contexts
 * - Audit logging and compliance
 * - Analytics and reporting
 * 
 * In our ticket example, these listeners demonstrate how events can trigger
 * various side effects without coupling the domain logic to these concerns.
 */
@Component
@Slf4j
public class TicketEventListener {
    
    /**
     * Handles TicketCreatedEvent
     * This method demonstrates various side effects that might occur when a ticket is created
     */
    @EventListener
    @Async // This makes the event handling asynchronous
    public void handleTicketCreated(TicketCreatedEvent event) {
        log.info("Handling TicketCreatedEvent: {}", event);
        
        try {
            // 1. Send confirmation email to customer
            sendConfirmationEmail(event);
            
            // 2. Update inventory systems
            updateInventory(event);
            
            // 3. Log for audit purposes
            auditTicketCreation(event);
            
            // 4. Trigger pricing analytics
            updatePricingAnalytics(event);
            
            // 5. Notify external systems (airline operations, etc.)
            notifyExternalSystems(event);
            
            log.info("Successfully processed TicketCreatedEvent for ticket: {}", event.getTicketId());
            
        } catch (Exception e) {
            log.error("Error processing TicketCreatedEvent: {}", event, e);
            // In production, you might want to:
            // - Retry the operation
            // - Send to a dead letter queue
            // - Trigger alerts
        }
    }
    
    /**
     * Handles TicketConfirmedEvent
     * This occurs when a ticket is confirmed (typically after payment)
     */
    @EventListener
    @Async
    public void handleTicketConfirmed(TicketConfirmedEvent event) {
        log.info("Handling TicketConfirmedEvent: {}", event);
        
        try {
            // 1. Send boarding pass to customer
            sendBoardingPass(event);
            
            // 2. Update seat inventory
            updateSeatInventory(event);
            
            // 3. Notify airline operations
            notifyAirlineOperations(event);
            
            // 4. Update loyalty program
            updateLoyaltyProgram(event);
            
            log.info("Successfully processed TicketConfirmedEvent for ticket: {}", event.getTicketId());
            
        } catch (Exception e) {
            log.error("Error processing TicketConfirmedEvent: {}", event, e);
        }
    }
    
    /**
     * Handles TicketCancelledEvent
     * This occurs when a ticket is cancelled
     */
    @EventListener
    @Async
    public void handleTicketCancelled(TicketCancelledEvent event) {
        log.info("Handling TicketCancelledEvent: {}", event);
        
        try {
            // 1. Process refund
            processRefund(event);
            
            // 2. Release seat inventory
            releaseSeatInventory(event);
            
            // 3. Send cancellation confirmation
            sendCancellationConfirmation(event);
            
            // 4. Update analytics
            updateCancellationAnalytics(event);
            
            log.info("Successfully processed TicketCancelledEvent for ticket: {}", event.getTicketId());
            
        } catch (Exception e) {
            log.error("Error processing TicketCancelledEvent: {}", event, e);
        }
    }
    
    /**
     * Handles TicketUsedEvent
     * This occurs when a ticket is used (passenger has traveled)
     */
    @EventListener
    @Async
    public void handleTicketUsed(TicketUsedEvent event) {
        log.info("Handling TicketUsedEvent: {}", event);
        
        try {
            // 1. Update loyalty points
            awardLoyaltyPoints(event);
            
            // 2. Send post-flight survey
            sendPostFlightSurvey(event);
            
            // 3. Update travel history
            updateTravelHistory(event);
            
            // 4. Trigger marketing campaigns
            triggerMarketingCampaigns(event);
            
            log.info("Successfully processed TicketUsedEvent for ticket: {}", event.getTicketId());
            
        } catch (Exception e) {
            log.error("Error processing TicketUsedEvent: {}", event, e);
        }
    }
    
    // Private helper methods that simulate various side effects
    // In a real system, these would integrate with actual external services
    
    private void sendConfirmationEmail(TicketCreatedEvent event) {
        log.info("Sending confirmation email for ticket: {} to customer: {}", 
                event.getTicketId(), event.getCustomerId());
        // Integrate with email service
    }
    
    private void updateInventory(TicketCreatedEvent event) {
        log.info("Updating inventory for flight: {} route: {}→{}", 
                event.getFlightNumber(), event.getOrigin(), event.getDestination());
        // Integrate with inventory management system
    }
    
    private void auditTicketCreation(TicketCreatedEvent event) {
        log.info("Auditing ticket creation: {} at price: {}", 
                event.getTicketId(), event.getPrice());
        // Store in audit log
    }
    
    private void updatePricingAnalytics(TicketCreatedEvent event) {
        log.info("Updating pricing analytics for route: {}→{} price: {}", 
                event.getOrigin(), event.getDestination(), event.getPrice());
        // Send to analytics system
    }
    
    private void notifyExternalSystems(TicketCreatedEvent event) {
        log.info("Notifying external systems about ticket creation: {}", event.getTicketId());
        // Integrate with airline operations, partner systems, etc.
    }
    
    private void sendBoardingPass(TicketConfirmedEvent event) {
        log.info("Sending boarding pass for ticket: {}", event.getTicketId());
        // Generate and send boarding pass
    }
    
    private void updateSeatInventory(TicketConfirmedEvent event) {
        log.info("Updating seat inventory for confirmed ticket: {}", event.getTicketId());
        // Update seat management system
    }
    
    private void notifyAirlineOperations(TicketConfirmedEvent event) {
        log.info("Notifying airline operations about confirmed ticket: {}", event.getTicketId());
        // Notify crew scheduling, catering, etc.
    }
    
    private void updateLoyaltyProgram(TicketConfirmedEvent event) {
        log.info("Updating loyalty program for customer: {}", event.getCustomerId());
        // Update frequent flyer program
    }
    
    private void processRefund(TicketCancelledEvent event) {
        log.info("Processing refund for cancelled ticket: {}", event.getTicketId());
        // Integrate with payment system
    }
    
    private void releaseSeatInventory(TicketCancelledEvent event) {
        log.info("Releasing seat inventory for cancelled ticket: {}", event.getTicketId());
        // Update inventory system
    }
    
    private void sendCancellationConfirmation(TicketCancelledEvent event) {
        log.info("Sending cancellation confirmation for ticket: {}", event.getTicketId());
        // Send confirmation email/SMS
    }
    
    private void updateCancellationAnalytics(TicketCancelledEvent event) {
        log.info("Updating cancellation analytics for ticket: {}", event.getTicketId());
        // Update business intelligence systems
    }
    
    private void awardLoyaltyPoints(TicketUsedEvent event) {
        log.info("Awarding loyalty points for used ticket: {}", event.getTicketId());
        // Update loyalty program
    }
    
    private void sendPostFlightSurvey(TicketUsedEvent event) {
        log.info("Sending post-flight survey for ticket: {}", event.getTicketId());
        // Send customer satisfaction survey
    }
    
    private void updateTravelHistory(TicketUsedEvent event) {
        log.info("Updating travel history for customer: {}", event.getCustomerId());
        // Update customer profile
    }
    
    private void triggerMarketingCampaigns(TicketUsedEvent event) {
        log.info("Triggering marketing campaigns for customer: {}", event.getCustomerId());
        // Trigger personalized offers
    }
}
