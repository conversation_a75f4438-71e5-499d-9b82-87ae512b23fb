package com.example.ddd.aviation.application.dto;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * TicketDto
 * 
 * This is a Query DTO that represents ticket data for external consumption.
 * Query DTOs are used to return data from the system without exposing internal domain objects.
 * 
 * Key characteristics of Query DTOs:
 * - Immutable
 * - No business logic
 * - Optimized for specific use cases (may not include all domain object fields)
 * - Can aggregate data from multiple domain objects
 * - Provide a stable interface for external consumers
 * 
 * Benefits of using DTOs:
 * - Decouples external interface from internal domain model
 * - Allows domain model to evolve without breaking external contracts
 * - Can be optimized for specific presentation needs
 * - Prevents accidental exposure of sensitive domain data
 */
@Getter
@RequiredArgsConstructor
public final class TicketDto {
    
    private final String ticketId;
    private final String status;
    
    // Customer information
    private final String customerId;
    private final String customerName;
    private final String customerEmail;
    
    // Flight information
    private final String flightNumber;
    private final String origin;
    private final String destination;
    private final String route;
    private final LocalDateTime departureTime;
    
    // Pricing information
    private final BigDecimal priceAmount;
    private final String priceCurrency;
    private final String formattedPrice;
    
    // Metadata
    private final LocalDateTime createdAt;
    private final LocalDateTime lastModifiedAt;
    private final boolean isDomesticFlight;
    
    @Override
    public String toString() {
        return String.format("TicketDto{id=%s, status=%s, customer=%s, flight=%s, route=%s, price=%s}",
                           ticketId, status, customerName, flightNumber, route, formattedPrice);
    }
}
