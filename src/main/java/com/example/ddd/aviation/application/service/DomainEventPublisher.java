package com.example.ddd.aviation.application.service;

import com.example.ddd.aviation.domain.model.DomainEvent;

/**
 * DomainEventPublisher Interface
 * 
 * This interface defines the contract for publishing domain events.
 * It's defined in the application layer because it's used by application services,
 * but the implementation will be in the infrastructure layer.
 * 
 * This follows the Dependency Inversion Principle:
 * - High-level modules (application) don't depend on low-level modules (infrastructure)
 * - Both depend on abstractions (this interface)
 * 
 * The publisher is responsible for:
 * - Taking domain events and making them available to event handlers
 * - Ensuring events are published reliably
 * - Handling any technical concerns around event publishing
 */
public interface DomainEventPublisher {
    
    /**
     * Publishes a domain event to all registered listeners
     * 
     * @param event the domain event to publish
     */
    void publish(DomainEvent event);
}
