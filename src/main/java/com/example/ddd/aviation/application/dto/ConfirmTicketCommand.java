package com.example.ddd.aviation.application.dto;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * ConfirmTicketCommand DTO
 * 
 * Command DTO for confirming a ticket (typically after payment processing).
 * This represents a simple command that only needs the ticket identifier.
 */
@Getter
@RequiredArgsConstructor
public final class ConfirmTicketCommand {
    
    private final String ticketId;
    
    @Override
    public String toString() {
        return String.format("ConfirmTicketCommand{ticketId=%s}", ticketId);
    }
}
