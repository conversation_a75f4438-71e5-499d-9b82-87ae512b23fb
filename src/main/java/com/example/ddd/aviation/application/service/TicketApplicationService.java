package com.example.ddd.aviation.application.service;

import com.example.ddd.aviation.application.dto.ConfirmTicketCommand;
import com.example.ddd.aviation.application.dto.CreateTicketCommand;
import com.example.ddd.aviation.application.dto.TicketDto;
import com.example.ddd.aviation.domain.model.*;
import com.example.ddd.aviation.domain.repository.CustomerRepository;
import com.example.ddd.aviation.domain.repository.TicketRepository;
import com.example.ddd.aviation.domain.service.PricingService;
import com.example.ddd.aviation.domain.service.TicketValidationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * TicketApplicationService
 * 
 * This is an Application Service in DDD. Application Services orchestrate use cases
 * and coordinate between different layers of the application.
 * 
 * Key responsibilities of Application Services:
 * - Orchestrate business workflows and use cases
 * - Coordinate between domain objects, repositories, and domain services
 * - Handle transaction boundaries
 * - Convert between DTOs and domain objects
 * - Publish domain events (or delegate to infrastructure)
 * - Provide a facade for external consumers
 * 
 * Application Services are different from Domain Services:
 * - Application Services orchestrate workflows (how)
 * - Domain Services contain business logic (what)
 * - Application Services are transaction boundaries
 * - Domain Services are stateless business operations
 * 
 * This service handles all ticket-related use cases and provides
 * a clean interface for the API layer.
 */
@Service
@RequiredArgsConstructor
@Transactional
public class TicketApplicationService {
    
    private final TicketRepository ticketRepository;
    private final CustomerRepository customerRepository;
    private final PricingService pricingService;
    private final TicketValidationService ticketValidationService;
    private final DomainEventPublisher domainEventPublisher;
    
    /**
     * Creates a new ticket based on the provided command
     * 
     * This method orchestrates the entire ticket creation workflow:
     * 1. Validates the request
     * 2. Creates or retrieves customer
     * 3. Calculates pricing
     * 4. Creates the ticket aggregate
     * 5. Persists the ticket
     * 6. Publishes domain events
     * 
     * @param command the ticket creation command
     * @return DTO representing the created ticket
     */
    public TicketDto createTicket(CreateTicketCommand command) {
        // 1. Create or retrieve customer
        Customer customer = getOrCreateCustomer(command);
        
        // 2. Calculate price using domain service
        Money calculatedPrice = pricingService.calculateTicketPrice(
            command.getOrigin(),
            command.getDestination(),
            command.getDepartureTime(),
            command.getBookingTime(),
            command.isCustomerLoyal()
        );
        
        // 3. Generate ticket ID
        TicketId ticketId = TicketId.generate();
        
        // 4. Validate ticket creation using domain service
        ticketValidationService.validateTicketCreation(
            ticketId,
            customer,
            command.getFlightNumber(),
            command.getOrigin(),
            command.getDestination(),
            command.getDepartureTime()
        );
        
        // 5. Create ticket aggregate (this generates domain events)
        Ticket ticket = new Ticket(
            ticketId,
            customer,
            command.getFlightNumber(),
            command.getOrigin(),
            command.getDestination(),
            command.getDepartureTime(),
            calculatedPrice
        );
        
        // 6. Save ticket (this should be done before publishing events)
        Ticket savedTicket = ticketRepository.save(ticket);
        
        // 7. Publish domain events
        publishDomainEvents(savedTicket);
        
        // 8. Convert to DTO and return
        return convertToDto(savedTicket);
    }
    
    /**
     * Confirms a ticket (typically after payment processing)
     * 
     * @param command the confirmation command
     * @return DTO representing the confirmed ticket
     */
    public TicketDto confirmTicket(ConfirmTicketCommand command) {
        // 1. Find the ticket
        Ticket ticket = ticketRepository.findById(TicketId.of(command.getTicketId()))
            .orElseThrow(() -> new IllegalArgumentException("Ticket not found: " + command.getTicketId()));
        
        // 2. Confirm the ticket (this generates domain events)
        ticket.confirm();
        
        // 3. Save the updated ticket
        Ticket savedTicket = ticketRepository.save(ticket);
        
        // 4. Publish domain events
        publishDomainEvents(savedTicket);
        
        // 5. Convert to DTO and return
        return convertToDto(savedTicket);
    }
    
    /**
     * Retrieves a ticket by ID
     * 
     * @param ticketId the ticket ID
     * @return Optional containing the ticket DTO if found
     */
    @Transactional(readOnly = true)
    public Optional<TicketDto> getTicket(String ticketId) {
        return ticketRepository.findById(TicketId.of(ticketId))
            .map(this::convertToDto);
    }
    
    /**
     * Retrieves all tickets for a customer
     * 
     * @param customerId the customer ID
     * @return list of ticket DTOs
     */
    @Transactional(readOnly = true)
    public List<TicketDto> getTicketsByCustomer(String customerId) {
        return ticketRepository.findByCustomerId(CustomerId.of(customerId))
            .stream()
            .map(this::convertToDto)
            .toList();
    }
    
    /**
     * Gets or creates a customer based on the command data
     * This is a private helper method that encapsulates customer management logic
     */
    private Customer getOrCreateCustomer(CreateTicketCommand command) {
        CustomerId customerId = CustomerId.of(command.getCustomerId());
        
        return customerRepository.findById(customerId)
            .orElseGet(() -> {
                Customer newCustomer = new Customer(
                    customerId,
                    command.getCustomerFirstName(),
                    command.getCustomerLastName(),
                    command.getCustomerEmail(),
                    command.getCustomerPhoneNumber()
                );
                return customerRepository.save(newCustomer);
            });
    }
    
    /**
     * Publishes domain events from the aggregate
     * This is typically delegated to infrastructure layer
     */
    private void publishDomainEvents(Ticket ticket) {
        ticket.getDomainEvents().forEach(domainEventPublisher::publish);
        ticket.clearDomainEvents();
    }
    
    /**
     * Converts domain object to DTO
     * This mapping logic could be extracted to a separate mapper class
     */
    private TicketDto convertToDto(Ticket ticket) {
        return new TicketDto(
            ticket.getId().getValue(),
            ticket.getStatus().getDisplayName(),
            ticket.getCustomer().getCustomerId().getValue(),
            ticket.getCustomer().getFullName(),
            ticket.getCustomer().getEmail(),
            ticket.getFlightNumber(),
            ticket.getOrigin(),
            ticket.getDestination(),
            ticket.getFlightRoute(),
            ticket.getDepartureTime(),
            ticket.getPrice().getAmount(),
            ticket.getPrice().getCurrency().getCurrencyCode(),
            ticket.getPrice().toString(),
            ticket.getCreatedAt(),
            ticket.getLastModifiedAt(),
            ticket.isDomesticFlight()
        );
    }
}
