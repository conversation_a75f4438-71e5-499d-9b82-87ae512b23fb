package com.example.ddd.aviation.application.dto;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * CreateTicketCommand DTO
 * 
 * This is a Command DTO (Data Transfer Object) used in the Application Layer.
 * DTOs are simple data containers that transfer data between layers without business logic.
 * 
 * Commands represent requests to perform actions in the system.
 * They are part of the CQRS (Command Query Responsibility Segregation) pattern.
 * 
 * Key characteristics of Command DTOs:
 * - Immutable (all fields final)
 * - No business logic
 * - Validation can be applied at the application layer
 * - Represent user intent or external system requests
 * - Named with action verbs (Create, Update, Delete, etc.)
 * 
 * This command encapsulates all the information needed to create a ticket,
 * coming from external sources like REST APIs, message queues, or other systems.
 */
@Getter
@RequiredArgsConstructor
public final class CreateTicketCommand {
    
    /**
     * Customer information for the ticket
     */
    private final String customerId;
    private final String customerFirstName;
    private final String customerLastName;
    private final String customerEmail;
    private final String customerPhoneNumber;
    
    /**
     * Flight information
     */
    private final String flightNumber;
    private final String origin;
    private final String destination;
    private final LocalDateTime departureTime;
    
    /**
     * Pricing information
     * Note: In some cases, price might be calculated by the system
     * rather than provided by the client
     */
    private final BigDecimal priceAmount;
    private final String priceCurrency;
    
    /**
     * Additional booking context
     */
    private final LocalDateTime bookingTime;
    private final boolean isCustomerLoyal;
    
    @Override
    public String toString() {
        return String.format("CreateTicketCommand{customerId=%s, flight=%s, route=%s→%s, departure=%s, price=%s %s}",
                           customerId, flightNumber, origin, destination, departureTime, priceAmount, priceCurrency);
    }
}
