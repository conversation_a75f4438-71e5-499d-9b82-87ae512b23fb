---
type: "always_apply"
---

- We are using Java JDK 21.
- This project create for learning DDD and how it is implemented in Spring Boot.
- Aim to write simple and enough code to understand DDD and how can it be implemented in production grade codes.
- Write simple and enogh comments in to the code.
- Do not write unit tests.
- The project emphasizes Clean Code, SOLID, DRY, and other best practices to ensure clarity, maintainability, and extensibility.
- Use in memory H2 database.
- Use lombok
- It aims to model and enforce business rules around ticket pricing, such as seasonal discounts, last-minute deals, loyalty programs, and early bird offers.
- This Spring Boot project is designed to learn and demonstrate Domain-Driven Design (DDD) comprehensively in the context of the civil aviation industry, focusing on a Rule Management System for Flexible Ticket Pricing.

✅ DDD Layers:
	•	Domain Layer: business rules and logic
	•	Application Layer: use cases
	•	Infrastructure Layer: persistence
	•	API Layer: interface to the outside world

✅ Clean Code Practices:
	•	SOLID principles
	•	DRY & KISS principles
	•	Clear, meaningful names
	•	Comprehensive comments to explain why and how